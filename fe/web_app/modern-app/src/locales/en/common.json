{"nav": {"home": "Home", "download": "Download", "ranking": "Ranking", "shop": "Shop", "library": "Library", "vote": "Vote", "notice": "Notice", "profile": "Profile", "demo": "Components Demo", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "auth": {"login": {"title": "<PERSON><PERSON>", "description": "Sign in to your account", "username": "Username", "password": "Password", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "remember": "Remember me", "submit": "Sign In", "submitting": "Signing in...", "forgot": "Forgot password?", "register": "Don't have an account? Sign up", "success": "Login successful!", "error": "<PERSON><PERSON> failed, please check your username and password"}, "register": {"title": "Register", "description": "Create a new account", "username": "Username", "email": "Email", "emailCode": "Email Verification Code", "password": "Password", "confirmPassword": "Confirm Password", "birthday": "Birthday", "inviteCode": "Invite Code (Optional)", "usernamePlaceholder": "Enter username (5-12 alphanumeric)", "emailPlaceholder": "Enter email address", "emailCodePlaceholder": "Enter verification code", "passwordPlaceholder": "Enter password (6-16 characters)", "confirmPasswordPlaceholder": "Enter password again", "inviteCodePlaceholder": "Enter invite code (optional)", "sendCode": "Send Code", "sending": "Sending...", "submit": "Sign Up", "submitting": "Registering...", "login": "Already have an account? Sign in", "terms": "I agree to the Terms of Service and Privacy Policy", "readTerms": "Read Terms of Service", "termsTitle": "Terms of Service and Privacy Policy", "termsDescription": "Please read the following terms of service and privacy policy carefully. You can agree after scrolling to the bottom", "readingProgress": "Reading Progress", "confirmRead": "I have read and understand the above terms in full", "mustScrollToBottom": "Please scroll to the bottom to read the complete terms first", "scrollToBottomHint": "Please scroll to the bottom of the document to enable the agree option", "acceptTerms": "Agree and Continue", "success": "Registration successful! Please login", "error": "Registration failed, please try again", "emailCodeSent": "Verification code sent to your email", "emailCodeError": "Failed to send verification code, please try again", "validation": {"usernameRequired": "Please enter username", "usernameLength": "Username must be 5-12 characters", "usernameFormat": "Username can only contain letters and numbers", "emailRequired": "Please enter email address", "emailFormat": "Please enter a valid email address", "emailCodeRequired": "Please enter email verification code", "passwordRequired": "Please enter password", "passwordLength": "Password must be 6-16 characters", "passwordFormat": "Password contains unsupported characters", "confirmPasswordRequired": "Please confirm password", "passwordMismatch": "Passwords do not match", "birthdayRequired": "Please select birthday", "termsRequired": "Please agree to the Terms of Service and Privacy Policy"}}, "forgot": {"title": "Forgot Password", "description": "Enter your email address and we'll send you a reset link", "email": "Email Address", "submit": "Send Reset Link", "back": "Back to Login"}}, "profile": {"title": "Profile", "language": "Switch Language", "settings": "Settings", "logout": "Logout"}, "welcome": {"title": "Welcome to MagicMS", "subtitle": "Modern MapleStory Private Server Management System"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "yes": "Yes", "no": "No"}, "game": {"server": {"status": "Server Status", "online": "Online", "offline": "Offline", "maintenance": "Maintenance", "players": "Online Players", "uptime": "Uptime"}, "character": {"name": "Character Name", "level": "Level", "job": "Job", "guild": "Guild", "exp": "Experience", "fame": "Fame"}, "ranking": {"title": "Rankings", "level": "Level Ranking", "fame": "Fame Ranking", "guild": "Guild Ranking", "rank": "Rank", "change": "Change"}}, "home": {"welcome": "Welcome to MagicMS", "description": "Experience the most authentic MapleStory private server and embark on a magical adventure with players worldwide", "downloadNow": "Download Now", "registerAccount": "Register Account", "serverInfo": {"title": "Server Information", "description": "Real-time server status and rate information", "expRate": "EXP Rate", "mesoRate": "Meso Rate", "dropRate": "Drop Rate", "bossRate": "Boss Rate"}, "features": {"title": "Core Features", "description": "Explore the exciting features of MagicMS", "quickDownload": {"title": "Quick Download", "description": "One-click download of game client, start playing quickly"}, "ranking": {"title": "Rankings", "description": "View character and guild rankings, showcase your strength"}, "shop": {"title": "Shop", "description": "Purchase game items to enhance your gaming experience"}, "community": {"title": "Community", "description": "Join our community and interact with other players"}}, "gameFeatures": {"title": "Game Features", "description": "Unique game mechanics and custom features", "coreFeatures": "Core Features", "jobEnhancements": "Job Enhancements"}, "rankings": {"title": "Rankings", "description": "View top players on the server", "viewAll": "View Full Rankings"}}, "download": {"title": "Game Download", "description": "Download MagicMS game client and start your adventure", "clientInfo": {"title": "Client Information", "version": "Version", "size": "Size", "updateDate": "Update Date", "contains": "Includes 1280x720 HD and Normal Client"}, "downloadOptions": {"title": "Download Options", "googleDrive": "Google Drive Download", "googleDriveDesc": "Recommended for overseas users", "caiyunDrive": "Caiyun Drive Download", "caiyunCode": "Code: RtDU", "updateTool": "Update Tool Download", "updateToolDesc": "Login required"}, "systemRequirements": {"title": "System Requirements", "os": "Operating System", "osValue": "Windows 7/8/10/11", "memory": "Memory", "memoryValue": "4GB RAM", "storage": "Storage", "storageValue": "8GB available space", "graphics": "Graphics", "graphicsValue": "DirectX 9.0c compatible"}, "installGuide": {"title": "Installation Guide", "step1": "1. Extract ZIP file to your chosen directory", "step2": "2. Run update.exe to check if client is up to date", "step3": "3. Run \"Switch Client.bat\" and follow prompts to switch interface mode", "step4": "4. Run MapleStory.exe or MapleStoryHD.exe to start the game"}, "troubleshooting": {"title": "Troubleshooting", "toggleButton": "Click to view troubleshooting solutions", "q1": "Windows Defender: Threat detected", "a1": "Due to reverse engineering modifications to the client binary files, it may be falsely reported as malware. If you have security concerns, you can run it in an isolated virtual machine. Solution: Set exclusion items or folders in your antivirus software.", "q2": "When I open \"MapleStory.exe\", it says \"xxxx.dll\" is missing", "a2": "Update Microsoft Visual C++ runtime libraries. You can download the VC runtime collection here, or download directly from Microsoft's official website.", "q3": "When I press \"Login\", the button stays pressed and nothing happens", "a3": "Right-click MapleStory.exe — Properties — Set compatibility mode to Windows XP", "q4": "Login interface and shop interface display abnormally", "a4": "Before running the corresponding client, make sure to use the \"Switch Client.bat\" in the client to switch UI.wz to the appropriate mode", "q5": "Error Code: -2147467259", "a5": "Change screen resolution and try again. After successfully entering the login interface, you can change the resolution back to original", "q6": "Error Code: -2147221008", "a6": "Try the following methods one by one: 1. Right-click MapleStory.exe — Properties — Compatibility — Turn on/off compatibility mode 2. Right-click MapleStory.exe — Properties — Compatibility — Disable fullscreen optimization 3. Usually restarting can avoid this error", "q7": "Error Code: (The parameter is incorrect)", "a7": "Right-click MapleStory.exe — Properties — Set compatibility mode to Windows XP", "q8": "Error Code: (Unspecified error)", "a8": "Check your monitor's refresh rate. It works best at 60Hz refresh rate. If your monitor is set to 59Hz, MapleStory will not work.", "q9": "Game client suddenly disappears without any prompt", "a9": "Some extreme environments have compatibility issues, please contact developers", "q10": "Pet doesn't auto-heal HP/MP", "a10": "1. Re-extract the client and add the directory to whitelist to prevent third-party protection software from deleting client dependency files 2. Clean boot Windows, see Microsoft documentation 3. If conditions allow, try running the game in a virtual machine", "q11": "Still can't run?", "a11": "Contact developers through public email, we will be happy to help you."}, "support": {"title": "Technical Support", "description": "If you encounter issues during download or installation, please contact our support team", "email": "Contact Email", "community": "Join Community", "communityDesc": "Connect with other players and share experiences"}, "notice": {"inviteRequired": "MagicMS requires invitation code for registration. Ensure you have an account before downloading", "inviteDescription": "Please contact administrators or existing players to get an invitation code, otherwise you cannot register a new account", "languageWarning": "Game primarily supports Chinese with limited English support", "backupReminder": "Regular backup of game saves is recommended"}}, "ranking": {"title": "Game Rankings", "description": "View top players and guild rankings on the server", "tabs": {"level": "Level Ranking", "fame": "Fame Ranking", "quest": "Quest Ranking", "monsterbook": "Monster Book Ranking", "guild": "Guild Ranking"}, "filters": {"job": "<PERSON> Filter", "sort": "Sort By", "all": "All Jobs", "beginner": "<PERSON><PERSON><PERSON>", "warrior": "Warrior", "magician": "Magician", "bowman": "<PERSON>", "thief": "<PERSON>hief", "pirate": "Pirate", "cygnus": "<PERSON><PERSON><PERSON>", "aran": "<PERSON><PERSON>"}, "table": {"rank": "Rank", "avatar": "Avatar", "name": "Character Name", "job": "Job", "level": "Level", "exp": "Experience", "fame": "Fame", "quest": "Quests Completed", "monsterbook": "Monster Book", "guild": "Guild", "guildName": "Guild Name", "leader": "Leader", "members": "Members", "points": "Points", "notice": "Notice", "alliance": "Alliance"}, "pagination": {"total": "Total", "items": "records", "page": "pages", "prev": "Previous", "next": "Next", "firstPage": "Already at first page", "lastPage": "No more data"}, "loading": "Loading ranking data...", "error": "Failed to load ranking data", "noData": "No ranking data available", "refresh": "Refresh Data"}, "vote": {"title": "Vote for MagicMS", "subtitle": "Get daily rewards by voting", "description": "Vote for MagicMS on Gtop100 to receive in-game rewards and support server development", "rewards": {"title": "Voting Rewards", "daily": "Daily voting reward: 10,000 NX", "consecutive": "Consecutive voting bonus: +500 NX per day", "maximum": "Maximum cumulative reward: 20,000 NX"}, "instructions": {"title": "Voting Steps", "step1": "Enter your game account name below", "step2": "Click the vote button to redirect to Gtop100", "step3": "Complete the verification CAPTCHA on Gtop100", "step4": "Click the red \"Vote for MagicMS\" button", "step5": "<PERSON><PERSON><PERSON> will be credited within 5 minutes after voting"}, "form": {"accountLabel": "Account Name", "accountPlaceholder": "Enter your game account name", "submitButton": "Vote on Gtop100", "submitting": "Redirecting..."}, "warnings": {"title": "Important Notice", "complete": "You must complete the entire Gtop100 voting process to receive rewards", "delay": "After successful voting, rewards may take up to 5 minutes to be credited", "account": "Please ensure you enter the correct game account name"}, "errors": {"noAccount": "Please enter your account name", "serverError": "Voting failed, please try again later"}}, "notice": {"title": "Game Notices", "subtitle": "Latest game news and important announcements", "description": "Get the latest game updates, event information and important announcements", "list": {"title": "Notice List", "empty": "No notices available", "loading": "Loading...", "loadMore": "Load More", "viewDetail": "View Details"}, "detail": {"backToList": "Back to Notice List", "author": "Author", "publishTime": "Published", "updateTime": "Updated", "views": "Views", "category": "Category", "loading": "Loading...", "notFound": "Notice not found or has been deleted", "lastUpdate": "Last Updated"}, "categories": {"all": "All", "update": "Game Updates", "event": "Events", "maintenance": "Maintenance", "important": "Important"}}, "shop": {"title": "Cash Shop", "subtitle": "Purchase game items and equipment", "help": "Help", "help_title": "Cash Shop Help", "help_not_available": "Help content not available", "loading_poster": "Loading poster...", "poster_alt": "Shop poster", "categories": "Categories", "all_categories": "All Categories", "search": "Search", "search_placeholder": "Enter item name to search...", "price": "Price", "stock": "Stock", "stock_unlimited": "Adequate", "expiration": "Expiration", "unlimited": "Unlimited", "days": "days", "buy": "Buy", "gift": "Gift", "unavailable": "Unavailable", "no_items": "No items", "no_items_desc": "No items in current category, try other categories or search", "buy_confirm_title": "Confirm Purchase", "buy_confirm_desc": "Please confirm purchase information and select receiving character", "gift_confirm_title": "Confirm Gift", "gift_confirm_desc": "Please fill in recipient information", "select_character": "Select Character", "select_character_placeholder": "Please select character to receive item", "receiver_name": "Recipient Character Name", "receiver_name_placeholder": "Enter recipient's character name", "receiver_birthday": "Recipient Birthday", "sender_birthday": "Sender Birthday", "gift_notice": "Please ensure recipient information is accurate, gifts cannot be revoked", "cancel": "Cancel", "confirm_buy": "Confirm Purchase", "confirm_gift": "Confirm Gift", "buy_success": "Purchase successful", "buy_error": "Purchase failed", "gift_success": "Gift successful", "gift_error": "Gift failed", "load_characters_error": "Failed to load character list", "items_count": "{{total}} items total", "page_info": "Page {{current}} of {{total}}"}}