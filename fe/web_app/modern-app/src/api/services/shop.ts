import { request } from '../client';
import {
  CSTypeResponse,
  CSPosterResponse,
  CSItemListResponse,
  CSItemBuyRequest,
  CSItemGiftRequest,
  CSHelperResponse,
  ShopFilter,
} from '../../types';

/**
 * 商城相关API服务
 */
export class ShopService {
  /**
   * 获取商城物品类型
   */
  static async getShopTypes(): Promise<CSTypeResponse> {
    const data = await request.get<CSTypeResponse>('/api/v1/cashshop/type');
    return data;
  }

  /**
   * 获取商城首页海报
   */
  static async getShopPoster(): Promise<CSPosterResponse> {
    const data = await request.get<CSPosterResponse>('/api/v1/cashshop/poster');
    return data;
  }

  /**
   * 获取商城物品列表
   */
  static async getShopItems(filter: ShopFilter = {}): Promise<CSItemListResponse> {
    const { page = 1, limit = 15, category = '', keyword = '' } = filter;
    const data = await request.get<CSItemListResponse>('/api/v1/cashshop/items', {
      params: {
        page,
        limit,
        category,
        keyword,
      },
    });
    return data;
  }

  /**
   * 购买商城物品
   */
  static async buyShopItem(request_data: CSItemBuyRequest): Promise<{ message: string }> {
    const data = await request.post<{ message: string }>('/api/v1/cashshop/buy', request_data);
    return data;
  }

  /**
   * 赠送商城物品
   */
  static async giftShopItem(request_data: CSItemGiftRequest): Promise<{ message: string }> {
    const data = await request.post<{ message: string }>('/api/v1/cashshop/gift', request_data);
    return data;
  }

  /**
   * 获取商城使用帮助
   */
  static async getShopHelper(): Promise<CSHelperResponse> {
    const data = await request.get<CSHelperResponse>('/api/csh');
    return data;
  }
}
