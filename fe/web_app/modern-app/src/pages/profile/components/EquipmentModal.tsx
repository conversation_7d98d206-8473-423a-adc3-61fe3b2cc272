import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/Dialog';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Badge } from '../../../components/ui/Badge';
import { Alert, AlertDescription } from '../../../components/ui/Alert';
import { LoadingSpinner } from '../../../components/common';
import { UserService } from '../../../api/services/user';
import type { EquipItemModel, CharInfo } from '../../../types';
import {
  Shield,
  Sword,
  Crown,
  Shirt,
  Circle,
  Hand,
  Gem,
  AlertCircle,
  RefreshCw,
  Star,
  TrendingUp
} from 'lucide-react';

interface EquipmentModalProps {
  character: CharInfo | null;
  isOpen: boolean;
  onClose: () => void;
}

// 装备位置映射
const EQUIPMENT_SLOTS = {
  '-1': { name: '帽子', icon: Crown, color: 'bg-purple-100 text-purple-800' },
  '-2': { name: '脸饰', icon: Gem, color: 'bg-pink-100 text-pink-800' },
  '-3': { name: '眼饰', icon: Gem, color: 'bg-blue-100 text-blue-800' },
  '-4': { name: '耳环', icon: Gem, color: 'bg-yellow-100 text-yellow-800' },
  '-5': { name: '上衣', icon: Shirt, color: 'bg-green-100 text-green-800' },
  '-6': { name: '裤子', icon: Shirt, color: 'bg-green-100 text-green-800' },
  '-7': { name: '鞋子', icon: Circle, color: 'bg-brown-100 text-brown-800' },
  '-8': { name: '手套', icon: Hand, color: 'bg-gray-100 text-gray-800' },
  '-9': { name: '披风', icon: Shirt, color: 'bg-indigo-100 text-indigo-800' },
  '-10': { name: '盾牌', icon: Shield, color: 'bg-red-100 text-red-800' },
  '-11': { name: '武器', icon: Sword, color: 'bg-orange-100 text-orange-800' },
  '-12': { name: '戒指1', icon: Gem, color: 'bg-purple-100 text-purple-800' },
  '-13': { name: '戒指2', icon: Gem, color: 'bg-purple-100 text-purple-800' },
  '-14': { name: '戒指3', icon: Gem, color: 'bg-purple-100 text-purple-800' },
  '-15': { name: '戒指4', icon: Gem, color: 'bg-purple-100 text-purple-800' },
  '-16': { name: '项链', icon: Gem, color: 'bg-yellow-100 text-yellow-800' },
};

export const EquipmentModal: React.FC<EquipmentModalProps> = ({
  character,
  isOpen,
  onClose,
}) => {
  const { t } = useTranslation('profile');
  const [equipment, setEquipment] = useState<EquipItemModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载装备数据
  const loadEquipment = async () => {
    if (!character) return;

    try {
      setLoading(true);
      setError(null);
      const data = await UserService.getCharacterEquipment(character.id);
      setEquipment(data.items || []);
    } catch (err: any) {
      console.error('Failed to load equipment:', err);
      setError(err.response?.data?.message || err.message || t('equipment.errors.loadFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && character) {
      loadEquipment();
    }
  }, [isOpen, character]);

  // 重试加载
  const handleRetry = () => {
    loadEquipment();
  };

  // 获取装备位置信息
  const getSlotInfo = (position: number) => {
    const slotKey = position.toString();
    return EQUIPMENT_SLOTS[slotKey as keyof typeof EQUIPMENT_SLOTS] || {
      name: `位置${position}`,
      icon: Gem,
      color: 'bg-gray-100 text-gray-800'
    };
  };

  // 渲染装备属性
  const renderEquipStats = (item: EquipItemModel) => {
    const stats = [];
    
    if (item.str) stats.push({ label: t('equipment.stats.str'), value: item.str });
    if (item.dex) stats.push({ label: t('equipment.stats.dex'), value: item.dex });
    if (item.int) stats.push({ label: t('equipment.stats.int'), value: item.int });
    if (item.luk) stats.push({ label: t('equipment.stats.luk'), value: item.luk });
    if (item.hp) stats.push({ label: t('equipment.stats.hp'), value: item.hp });
    if (item.mp) stats.push({ label: t('equipment.stats.mp'), value: item.mp });
    if (item.watk) stats.push({ label: t('equipment.stats.watk'), value: item.watk });
    if (item.matk) stats.push({ label: t('equipment.stats.matk'), value: item.matk });
    if (item.wdef) stats.push({ label: t('equipment.stats.wdef'), value: item.wdef });
    if (item.mdef) stats.push({ label: t('equipment.stats.mdef'), value: item.mdef });
    if (item.acc) stats.push({ label: t('equipment.stats.acc'), value: item.acc });
    if (item.avoid) stats.push({ label: t('equipment.stats.avoid'), value: item.avoid });
    if (item.speed) stats.push({ label: t('equipment.stats.speed'), value: item.speed });
    if (item.jump) stats.push({ label: t('equipment.stats.jump'), value: item.jump });

    return stats;
  };

  // 渲染装备卡片
  const renderEquipmentCard = (item: EquipItemModel) => {
    const slotInfo = getSlotInfo(item.position);
    const IconComponent = slotInfo.icon;
    const stats = renderEquipStats(item);

    return (
      <Card key={item.id} className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <IconComponent className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-sm font-medium">
                {slotInfo.name}
              </CardTitle>
            </div>
            <Badge variant="outline" className={slotInfo.color}>
              {t('equipment.position', { position: Math.abs(item.position) })}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* 道具ID和数量 */}
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">ID:</span>
              <span className="font-mono">{item.item_id}</span>
            </div>
            
            {item.quantity > 1 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('equipment.quantity')}:</span>
                <span>{item.quantity}</span>
              </div>
            )}

            {/* 强化等级 */}
            {item.upgrade_slots && item.upgrade_slots > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{t('equipment.upgrade')}:</span>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span className="text-green-600 font-medium">+{item.upgrade_slots}</span>
                </div>
              </div>
            )}

            {/* 装备属性 */}
            {stats.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs font-medium text-muted-foreground mb-2">
                  {t('equipment.attributes')}:
                </div>
                <div className="grid grid-cols-2 gap-1 text-xs">
                  {stats.map((stat, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-muted-foreground">{stat.label}:</span>
                      <span className="font-medium text-blue-600">+{stat.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 所有者信息 */}
            {item.owner && (
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{t('equipment.owner')}:</span>
                <span>{item.owner}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>
              {character ? 
                t('equipment.title', { characterName: character.name }) : 
                t('equipment.title', { characterName: '' })
              }
            </span>
          </DialogTitle>
          <DialogDescription>
            {t('equipment.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button onClick={handleRetry} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {t('common.retry')}
                </Button>
              </AlertDescription>
            </Alert>
          ) : equipment.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">{t('equipment.noEquipment')}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {equipment.map(renderEquipmentCard)}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
