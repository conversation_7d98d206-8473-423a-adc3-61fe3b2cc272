import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { HelpCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';

// Components
import { Button } from '../../components/ui/Button';
import PaginationComponent from '../../components/ui/PaginationComponent';
import ShopPoster from '../../components/shop/ShopPoster';
import ShopCategoryFilter from '../../components/shop/ShopCategoryFilter';
import ShopSearch from '../../components/shop/ShopSearch';
import ShopItemList from '../../components/shop/ShopItemList';
import BuyConfirmDialog from '../../components/shop/BuyConfirmDialog';
import GiftConfirmDialog from '../../components/shop/GiftConfirmDialog';
import ShopHelpDialog from '../../components/shop/ShopHelpDialog';

// Services & Types
import { ShopService } from '../../api/services/shop';
import {
  CSPoster,
  CSItemType,
  CSItem,
  CSItemListResponse,
  ShopFilter,
  CSItemBuyRequest,
  CSItemGiftRequest,
} from '../../types';

const ShopPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // State
  const [poster, setPoster] = useState<CSPoster | null>(null);
  const [categories, setCategories] = useState<CSItemType[]>([]);
  const [items, setItems] = useState<CSItem[]>([]);
  const [total, setTotal] = useState(0);
  const [helpContent, setHelpContent] = useState<string | null>(null);

  // Loading states
  const [posterLoading, setPosterLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [itemsLoading, setItemsLoading] = useState(false);
  const [helpLoading, setHelpLoading] = useState(false);

  // Filter states
  const [filter, setFilter] = useState<ShopFilter>({
    page: 1,
    limit: 15,
    category: '',
    keyword: '',
  });

  // Dialog states
  const [buyDialogOpen, setBuyDialogOpen] = useState(false);
  const [giftDialogOpen, setGiftDialogOpen] = useState(false);
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<CSItem | null>(null);

  // Load initial data
  useEffect(() => {
    loadPoster();
    loadCategories();
  }, []);

  // Load items when filter changes
  useEffect(() => {
    loadItems();
  }, [filter.page, filter.limit, filter.category, filter.keyword]);

  // Load poster
  const loadPoster = async () => {
    try {
      setPosterLoading(true);
      const response = await ShopService.getShopPoster();
      setPoster(response);
    } catch (error) {
      console.error('Failed to load poster:', error);
      // Don't show error toast for poster as it's not critical
    } finally {
      setPosterLoading(false);
    }
  };

  // Load categories
  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await ShopService.getShopTypes();
      setCategories(response.items);
    } catch (error) {
      console.error('Failed to load categories:', error);
      toast.error(t('common:error.load_failed'));
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Load items
  const loadItems = async () => {
    try {
      setItemsLoading(true);
      const response: CSItemListResponse = await ShopService.getShopItems(filter);
      setItems(response.items);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load items:', error);
      toast.error(t('common:error.load_failed'));
    } finally {
      setItemsLoading(false);
    }
  };

  // Load help content
  const loadHelpContent = async () => {
    try {
      setHelpLoading(true);
      const response = await ShopService.getShopHelper();
      setHelpContent(response.content);
    } catch (error) {
      console.error('Failed to load help content:', error);
      setHelpContent(null);
    } finally {
      setHelpLoading(false);
    }
  };

  // Handle category change
  const handleCategoryChange = useCallback((categoryId: string) => {
    setFilter(prev => ({
      ...prev,
      category: categoryId,
      page: 1, // Reset to first page
    }));
  }, []);

  // Handle search
  const handleSearch = useCallback((keyword: string) => {
    setFilter(prev => ({
      ...prev,
      keyword,
      page: 1, // Reset to first page
    }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilter(prev => ({
      ...prev,
      page,
    }));
  }, []);

  // Check login status
  const checkLoginStatus = useCallback(() => {
    if (!isAuthenticated) {
      toast.error(t('common:auth.login_required'));
      navigate('/login');
      return false;
    }
    return true;
  }, [isAuthenticated, navigate, t]);

  // Handle buy item
  const handleBuyItem = useCallback((item: CSItem) => {
    if (!checkLoginStatus()) return;
    setSelectedItem(item);
    setBuyDialogOpen(true);
  }, [checkLoginStatus]);

  // Handle gift item
  const handleGiftItem = useCallback((item: CSItem) => {
    if (!checkLoginStatus()) return;
    setSelectedItem(item);
    setGiftDialogOpen(true);
  }, [checkLoginStatus]);

  // Handle buy confirm
  const handleBuyConfirm = async (item: CSItem, characterId: number) => {
    try {
      const request: CSItemBuyRequest = {
        shop_id: item.id,
        character_id: characterId,
      };
      await ShopService.buyShopItem(request);
      toast.success(t('common:shop.buy_success'));
      // Refresh items to update stock
      loadItems();
    } catch (error) {
      console.error('Failed to buy item:', error);
      toast.error(t('common:shop.buy_error'));
    }
  };

  // Handle gift confirm
  const handleGiftConfirm = async (item: CSItem, accept: string, birthday: string) => {
    try {
      const request: CSItemGiftRequest = {
        shop_id: item.id,
        accept,
        birthday,
      };
      await ShopService.giftShopItem(request);
      toast.success(t('common:shop.gift_success'));
      // Refresh items to update stock
      loadItems();
    } catch (error) {
      console.error('Failed to gift item:', error);
      toast.error(t('common:shop.gift_error'));
    }
  };

  // Handle help dialog
  const handleHelpClick = () => {
    if (!helpContent) {
      loadHelpContent();
    }
    setHelpDialogOpen(true);
  };

  // Calculate pagination
  const totalPages = Math.ceil(total / (filter.limit || 15));

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('common:shop.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('common:shop.subtitle')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadItems}
            disabled={itemsLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${itemsLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleHelpClick}
          >
            <HelpCircle className="w-4 h-4 mr-2" />
            {t('common:shop.help')}
          </Button>
        </div>
      </div>

      {/* Poster */}
      <ShopPoster
        poster={poster}
        loading={posterLoading}
        onPosterClick={() => {
          if (poster?.doc) {
            setHelpContent(poster.doc);
            setHelpDialogOpen(true);
          }
        }}
      />

      {/* Category Filter */}
      <ShopCategoryFilter
        categories={categories}
        selectedCategory={filter.category || ''}
        onCategoryChange={handleCategoryChange}
        loading={categoriesLoading}
      />

      {/* Search */}
      <ShopSearch
        onSearch={handleSearch}
        defaultValue={filter.keyword}
      />

      {/* Items Count */}
      {!itemsLoading && (
        <div className="flex items-center justify-between mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('common:shop.items_count', { total })}
          </p>
          {totalPages > 1 && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('common:shop.page_info', { current: filter.page, total: totalPages })}
            </p>
          )}
        </div>
      )}

      {/* Items List */}
      <ShopItemList
        items={items}
        loading={itemsLoading}
        onBuy={handleBuyItem}
        onGift={handleGiftItem}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8">
          <PaginationComponent
            currentPage={filter.page || 1}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* Dialogs */}
      <BuyConfirmDialog
        open={buyDialogOpen}
        onOpenChange={setBuyDialogOpen}
        item={selectedItem}
        onConfirm={handleBuyConfirm}
      />

      <GiftConfirmDialog
        open={giftDialogOpen}
        onOpenChange={setGiftDialogOpen}
        item={selectedItem}
        onConfirm={handleGiftConfirm}
      />

      <ShopHelpDialog
        open={helpDialogOpen}
        onOpenChange={setHelpDialogOpen}
        content={helpContent}
        loading={helpLoading}
      />
    </div>
  );
};

export default ShopPage;
