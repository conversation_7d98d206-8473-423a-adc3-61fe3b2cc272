import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Search } from 'lucide-react';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

interface ShopSearchProps {
  onSearch: (keyword: string) => void;
  placeholder?: string;
  defaultValue?: string;
}

const ShopSearch: React.FC<ShopSearchProps> = ({
  onSearch,
  placeholder,
  defaultValue = ''
}) => {
  const { t } = useTranslation();
  const [keyword, setKeyword] = useState(defaultValue);

  const handleSearch = () => {
    onSearch(keyword.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            type="text"
            placeholder={placeholder || t('common:shop.search_placeholder')}
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full"
          />
        </div>
        <Button
          onClick={handleSearch}
          className="px-4 py-2 flex items-center gap-2"
        >
          <Search className="w-4 h-4" />
          {t('common:shop.search')}
        </Button>
      </div>
    </div>
  );
};

export default ShopSearch;
