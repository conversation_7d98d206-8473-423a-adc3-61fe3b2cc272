import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { CSItemType } from '../../types';

interface ShopCategoryFilterProps {
  categories: CSItemType[];
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
  loading?: boolean;
}

const ShopCategoryFilter: React.FC<ShopCategoryFilterProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  loading = false
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex flex-wrap gap-2 mb-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="h-9 w-20 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"
          />
        ))}
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        {t('common:shop.categories')}
      </h3>
      <div className="flex flex-wrap gap-2">
        {/* 全部分类按钮 */}
        <Button
          variant={selectedCategory === '' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => onCategoryChange('')}
          className="transition-all duration-200"
        >
          {t('common:shop.all_categories')}
        </Button>
        
        {/* 分类按钮 */}
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id.toString() ? 'primary' : 'outline'}
            size="sm"
            onClick={() => onCategoryChange(category.id.toString())}
            className="transition-all duration-200"
          >
            {category.name}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default ShopCategoryFilter;
