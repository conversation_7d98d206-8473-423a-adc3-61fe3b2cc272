import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ShoppingCart, User, Clock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/Dialog';
import { Button } from '../ui/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/Select';
import { CSItem, CharInfo } from '../../types';
import { UserService } from '../../api/services/user';
import toast from 'react-hot-toast';

interface BuyConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: CSItem | null;
  onConfirm: (item: CSItem, characterId: number) => void;
}

const BuyConfirmDialog: React.FC<BuyConfirmDialogProps> = ({
  open,
  onOpenChange,
  item,
  onConfirm
}) => {
  const { t } = useTranslation();
  const [characters, setCharacters] = useState<CharInfo[]>([]);
  const [selectedCharacterId, setSelectedCharacterId] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // 获取角色列表
  useEffect(() => {
    if (open) {
      loadCharacters();
      setSelectedCharacterId('');
    }
  }, [open]);

  const loadCharacters = async () => {
    try {
      setLoading(true);
      const response = await UserService.getCharacterList();
      setCharacters(response.items);
    } catch (error) {
      console.error('Failed to load characters:', error);
      toast.error(t('common:shop.load_characters_error'));
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    if (!item || !selectedCharacterId) return;
    
    const characterId = parseInt(selectedCharacterId);
    onConfirm(item, characterId);
    onOpenChange(false);
  };

  // 获取有效期显示文本
  const getExpirationText = (item: CSItem) => {
    if (item.receive_method === 0) {
      return `60 ${t('common:shop.days')}`;
    }
    if (item.receive_method !== 0 && item.expiration !== null) {
      return `${Math.floor(item.expiration / 86400)} ${t('common:shop.days')}`;
    }
    return t('common:shop.unlimited');
  };

  if (!item) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            {t('common:shop.buy_confirm_title')}
          </DialogTitle>
          <DialogDescription>
            {t('common:shop.buy_confirm_desc')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 商品信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <img
                src={
                  item.item_ico !== null
                    ? item.item_ico.startsWith('http')
                      ? item.item_ico
                      : `data:image/png;base64,${item.item_ico}`
                    : `https://maplestory.io/api/GMS/253/item/${item.item_id}/iconRaw`
                }
                alt={item.title}
                className="w-12 h-12 object-contain rounded border border-gray-200 dark:border-gray-700"
              />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {item.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {item.desc}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">
                  {t('common:shop.price')}:
                </span>
                <span className="ml-2 font-bold text-orange-600 dark:text-orange-400">
                  {item.price} {item.currency}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-gray-500 dark:text-gray-400">
                  {t('common:shop.expiration')}:
                </span>
                <span className="ml-1 text-gray-700 dark:text-gray-300">
                  {getExpirationText(item)}
                </span>
              </div>
            </div>
          </div>

          {/* 角色选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <User className="w-4 h-4 inline mr-1" />
              {t('common:shop.select_character')}
            </label>
            <Select value={selectedCharacterId} onValueChange={setSelectedCharacterId}>
              <SelectTrigger>
                <SelectValue placeholder={t('common:shop.select_character_placeholder')} />
              </SelectTrigger>
              <SelectContent>
                {characters.map((character) => (
                  <SelectItem key={character.id} value={character.id.toString()}>
                    {character.name} (Lv.{character.level})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('common:shop.cancel')}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedCharacterId || loading}
          >
            {t('common:shop.confirm_buy')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BuyConfirmDialog;
