import React from 'react';
import { useTranslation } from 'react-i18next';
import { ShoppingCart, Gift, Clock, AlertCircle } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { CSItem } from '../../types';

interface ShopItemCardProps {
  item: CSItem;
  onBuy: (item: CSItem) => void;
  onGift: (item: CSItem) => void;
}

const ShopItemCard: React.FC<ShopItemCardProps> = ({ item, onBuy, onGift }) => {
  const { t } = useTranslation();

  // 获取商品图标URL
  const getItemIconUrl = (item: CSItem): string => {
    if (item.item_ico !== null) {
      if (item.item_ico.startsWith('http')) {
        return item.item_ico;
      } else {
        return `data:image/png;base64,${item.item_ico}`;
      }
    } else {
      return `https://maplestory.io/api/GMS/253/item/${item.item_id}/iconRaw`;
    }
  };

  // 检查商品是否可购买
  const canPurchase = () => {
    if (!item.can_buy) return false;
    if (item.amount !== null && item.amount <= 0) return false;
    
    const now = new Date().getTime();
    if (item.start_sale_time && now < new Date(item.start_sale_time.replace(/-/g, '/')).getTime()) {
      return false;
    }
    if (item.end_sale_time && now > new Date(item.end_sale_time.replace(/-/g, '/')).getTime()) {
      return false;
    }
    
    return true;
  };

  // 检查是否可赠送
  const canGift = () => {
    return canPurchase() && !item.ban_gift;
  };

  // 获取库存显示文本
  const getStockText = () => {
    if (item.amount === null) {
      return t('common:shop.stock_unlimited');
    }
    return `${t('common:shop.stock')}: ${item.amount}`;
  };

  // 获取有效期显示文本
  const getExpirationText = () => {
    if (item.receive_method === 0) {
      return `60 ${t('common:shop.days')}`;
    }
    if (item.receive_method !== 0 && item.expiration !== null) {
      return `${Math.floor(item.expiration / 86400)} ${t('common:shop.days')}`;
    }
    return t('common:shop.unlimited');
  };

  return (
    <Card className="h-full hover:shadow-lg transition-shadow duration-200">
      <CardContent className="p-4">
        <div className="flex gap-4">
          {/* 商品图标 */}
          <div className="flex-shrink-0">
            <img
              src={getItemIconUrl(item)}
              alt={item.title}
              className="w-16 h-16 object-contain rounded-lg border border-gray-200 dark:border-gray-700"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/static/images/default-item.png';
              }}
            />
          </div>

          {/* 商品信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                {item.title}
              </h3>
              {!canPurchase() && (
                <Badge variant="secondary" className="ml-2">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {t('common:shop.unavailable')}
                </Badge>
              )}
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
              {item.desc}
            </p>

            {/* 价格和库存信息 */}
            <div className="space-y-1 mb-3">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t('common:shop.price')}:
                </span>
                <span className="font-bold text-orange-600 dark:text-orange-400">
                  {item.price}
                </span>
                <span className="text-sm text-green-600 dark:text-green-400">
                  {item.currency}
                </span>
              </div>
              
              <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                <span>{getStockText()}</span>
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {getExpirationText()}
                </span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => onBuy(item)}
                disabled={!canPurchase()}
                className="flex-1 flex items-center justify-center gap-1"
              >
                <ShoppingCart className="w-4 h-4" />
                {t('common:shop.buy')}
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => onGift(item)}
                disabled={!canGift()}
                className="flex-1 flex items-center justify-center gap-1"
              >
                <Gift className="w-4 h-4" />
                {t('common:shop.gift')}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShopItemCard;
