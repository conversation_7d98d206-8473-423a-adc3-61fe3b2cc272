import React from 'react';
import { useTranslation } from 'react-i18next';
import { Package } from 'lucide-react';
import ShopItemCard from './ShopItemCard';
import { CSItem } from '../../types';

interface ShopItemListProps {
  items: CSItem[];
  loading?: boolean;
  onBuy: (item: CSItem) => void;
  onGift: (item: CSItem) => void;
}

const ShopItemList: React.FC<ShopItemListProps> = ({
  items,
  loading = false,
  onBuy,
  onGift
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex gap-4">
              <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-lg" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded w-3/4" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 animate-pulse rounded w-full" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 animate-pulse rounded w-1/2" />
                <div className="flex gap-2 mt-3">
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 animate-pulse rounded flex-1" />
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 animate-pulse rounded flex-1" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('common:shop.no_items')}
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          {t('common:shop.no_items_desc')}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {items.map((item) => (
        <ShopItemCard
          key={item.id}
          item={item}
          onBuy={onBuy}
          onGift={onGift}
        />
      ))}
    </div>
  );
};

export default ShopItemList;
