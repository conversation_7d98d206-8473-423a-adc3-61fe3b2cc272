import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Gift, User, Calendar, Clock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/Dialog';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { CSItem } from '../../types';

interface GiftConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: CSItem | null;
  onConfirm: (item: CSItem, accept: string, birthday: string) => void;
}

const GiftConfirmDialog: React.FC<GiftConfirmDialogProps> = ({
  open,
  onOpenChange,
  item,
  onConfirm
}) => {
  const { t } = useTranslation();
  const [accept, setAccept] = useState('');
  const [birthday, setBirthday] = useState('');

  const handleConfirm = () => {
    if (!item || !accept.trim() || !birthday) return;
    
    onConfirm(item, accept.trim(), birthday);
    onOpenChange(false);
    // 清空表单
    setAccept('');
    setBirthday('');
  };

  const handleClose = () => {
    onOpenChange(false);
    // 清空表单
    setAccept('');
    setBirthday('');
  };

  // 获取有效期显示文本
  const getExpirationText = (item: CSItem) => {
    if (item.receive_method === 0) {
      return `60 ${t('common:shop.days')}`;
    }
    if (item.receive_method === 9 && item.expiration !== null) {
      return `${Math.floor(item.expiration / 86400)} ${t('common:shop.days')}`;
    }
    return t('common:shop.unlimited');
  };

  if (!item) return null;

  const isFormValid = accept.trim() && birthday;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="w-5 h-5" />
            {t('common:shop.gift_confirm_title')}
          </DialogTitle>
          <DialogDescription>
            {t('common:shop.gift_confirm_desc')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 商品信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <img
                src={
                  item.item_ico !== null
                    ? item.item_ico.startsWith('http')
                      ? item.item_ico
                      : `data:image/png;base64,${item.item_ico}`
                    : `https://maplestory.io/api/GMS/253/item/${item.item_id}/iconRaw`
                }
                alt={item.title}
                className="w-12 h-12 object-contain rounded border border-gray-200 dark:border-gray-700"
              />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {item.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {item.desc}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">
                  {t('common:shop.price')}:
                </span>
                <span className="ml-2 font-bold text-orange-600 dark:text-orange-400">
                  {item.price} {item.currency}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-gray-500 dark:text-gray-400">
                  {t('common:shop.expiration')}:
                </span>
                <span className="ml-1 text-gray-700 dark:text-gray-300">
                  {getExpirationText(item)}
                </span>
              </div>
            </div>
          </div>

          {/* 接收人信息 */}
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                {t('common:shop.receiver_name')}
              </label>
              <Input
                type="text"
                placeholder={t('common:shop.receiver_name_placeholder')}
                value={accept}
                onChange={(e) => setAccept(e.target.value)}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                {t('common:shop.sender_birthday')}
              </label>
              <Input
                type="date"
                value={birthday}
                onChange={(e) => setBirthday(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* 提示信息 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {t('common:shop.gift_notice')}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t('common:shop.cancel')}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!isFormValid}
          >
            {t('common:shop.confirm_gift')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GiftConfirmDialog;
