import React from 'react';
import { useTranslation } from 'react-i18next';
import { CSPoster } from '../../types';

interface ShopPosterProps {
  poster: CSPoster | null;
  onPosterClick?: () => void;
  loading?: boolean;
}

const ShopPoster: React.FC<ShopPosterProps> = ({ poster, onPosterClick, loading = false }) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-lg flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">
          {t('common:shop.loading_poster')}
        </div>
      </div>
    );
  }

  if (!poster || !poster.href) {
    return null;
  }

  const handleClick = () => {
    if (poster.redirect) {
      window.open(poster.redirect, '_blank');
    } else if (onPosterClick) {
      onPosterClick();
    }
  };

  return (
    <div className="w-full text-center mb-6">
      <div 
        className="inline-block cursor-pointer transition-transform hover:scale-105"
        onClick={handleClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        <img
          src={poster.href}
          alt={poster.title || t('common:shop.poster_alt')}
          title={poster.title}
          className="max-w-full h-auto rounded-lg shadow-lg"
          style={{
            maxWidth: '100%',
            margin: '0 auto'
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
          }}
        />
      </div>
    </div>
  );
};

export default ShopPoster;
