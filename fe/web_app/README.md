# MapleStory Web Application

一个MapleStory私人服务器（MagicMS）的Web应用，它提供注册、签到、公告、商城、资料库等社区功能。

# 项目重构

这个应用已经被完整的实现在了[demo](demo)，出于历史原因，早期的实现使用了一些“非现代化”的工具和代码，本项目的目标是使用现代化前端技术重头构建一个web应用。

该项目的后端应用独立实现，不在本项目仓库的考虑范围内，API接口见 [openapi.json](openapi.json)


## 主要技术栈

- React 19 (客户端渲染、ULR路由)
- TypeScript
- Tailwind CSS
- Shadcn UI

## 核心功能

- [ ] i18n国际化支持
- [ ] 主页
- [ ] 下载页
- [ ] 排行页
- [ ] 投票页
- [ ] 公告页
- [ ] 商城页
- [ ] 资料库

其中，排行榜、商城页和资料库是重点重构功能，我们需要使用可复用的组件化方式来替代早期的html字符串填充，增加代码复用程度和可维护性。

## 代码分析结果

### 当前技术栈分析
- **React版本**: 17.0.1 (需要升级到React 19)
- **路由**: React Router v5 (需要升级到v6)
- **TypeScript**: 4.9.5 (需要升级到最新版本)
- **构建工具**: Create React App (考虑迁移到Vite)
- **样式**: 传统CSS + Bootstrap (需要迁移到Tailwind CSS)
- **国际化**: i18next (已有良好基础，需要优化)

### 现有功能模块分析
1. **主页 (Home.js)**: 基础展示页面，使用传统组件方式
2. **排行榜 (Rank.js)**: 复杂的HTML字符串拼接，急需组件化重构
3. **商城 (CashShop.js)**: 商品展示逻辑复杂，需要现代化重构
4. **资料库 (Library.js)**: 物品搜索和展示功能，代码量大，需要拆分
5. **公告系统 (Notice.js)**: 支持Markdown，但UI需要现代化
6. **下载页/投票页**: 相对简单，但需要响应式优化

### 技术债务识别
- 大量HTML字符串拼接，缺乏组件化
- 样式系统混乱，Bootstrap + 自定义CSS
- 缺乏统一的状态管理
- API调用分散，缺乏统一抽象
- 缺乏TypeScript类型定义
- 缺乏现代化的构建优化

## 重构计划

见[README.md](modern-app/README.md)